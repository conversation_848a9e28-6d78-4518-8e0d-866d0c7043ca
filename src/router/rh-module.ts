import type { RouteRecordRaw } from 'vue-router'

const rhModuleRoutes: RouteRecordRaw[] = [
    {
        path: "/rh",
        name: "rh-module-home",
        component: () => import('../app/rh/MainDashRh.vue')
    },
    {
        path: "/rh/saisie/personnel",
        name: "rh-module-personnel",
        component: () => import('../app/rh/ListePersonnel.vue')
    },
    {
        path: "/rh/saisie/personnel/nouveau",
        name: "rh-module-nouveau-personnel",
        component: () => import('../app/rh/ListePersonnel.vue')
    },
    {
        path: "/rh/saisie/presence",
        name: "rh-module-presence",
        component: () => import('../app/rh/ListePresence.vue')
    },
    {
        path: "/rh/saisie/conge",
        name: "rh-module-conge",
        component: () => import('../app/rh/ListeConge.vue')
    }, 

    //saisie prealable
    /**
     * Personnel
     * Presence
     * Mise en place personnel
     * Salaire
     * Evaluation
     * Formation continue
     * Conge
     */
]

export { rhModuleRoutes }