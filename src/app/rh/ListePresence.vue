<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import LayoutSaisieOperation from '@/components/templates/LayoutSaisieOperation.vue';
import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';
import SaisieRhLayout from '@/components/templates/rh/SaisieRhLayout.vue';



const studentsPresences = [
    {
        id: 1,
        nom: "Mwamba",
        postnom: "Kabasele",
        prenom: "Jean",
        matricule: "ETU001",
        sexe: "M",
        filiere: "Lettres",
        classe: "1ère",
        annee: "2024-2025",
        cycle: "Cycle 1",
        present: true,
        commentaire: "Présent et attentif"
    },
    {
        id: 2,
        nom: "Kason<PERSON>",
        postnom: "Mutombo",
        prenom: "<PERSON>",
        matricule: "ETU002",
        sexe: "F",
        filiere: "Sciences",
        classe: "2ème",
        annee: "2024-2025",
        cycle: "Cycle 1",
        present: false,
        commentaire: "Absent pour raison médicale"
    },
    {
        id: 3,
        nom: "Ilunga",
        postnom: "Kabeya",
        prenom: "Patrick",
        matricule: "ETU003",
        sexe: "M",
        filiere: "Mathématiques",
        classe: "3ème",
        annee: "2024-2025",
        cycle: "Cycle 2",
        present: true,
        commentaire: "Présent"
    },
    {
        id: 4,
        nom: "Tshibanda",
        postnom: "Mbuyi",
        prenom: "Aline",
        matricule: "ETU004",
        sexe: "F",
        filiere: "Lettres",
        classe: "4ème",
        annee: "2024-2025",
        cycle: "Cycle 2",
        present: true,
        commentaire: "Présente et participative"
    },
    {
        id: 5,
        nom: "Kabongo",
        postnom: "Mukendi",
        prenom: "David",
        matricule: "ETU005",
        sexe: "M",
        filiere: "Sciences",
        classe: "1ère",
        annee: "2024-2025",
        cycle: "Cycle 1",
        present: false,
        commentaire: "Retard justifié"
    }
]
</script>

<template>
    <SaisieRhLayout activeBread="Presence" active-tag-name="presence" group="saisie">
        <BoxPanelWrapper>
            <div class="flex sm:items-center gap-3 flex-col sm:flex-row sm:justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher un personnel..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center sm:justify-end gap-2.5 flex-1">
                    <Button size="md" class="rounded-md max-sm:flex-1 sm:w-max" as-child>
                        <RouterLink to="/rh/operations/nouveau-personnel">
                            <span class="flex iconify hugeicons--plus-sign"></span>
                            <span class="hidden sm:flex">Nouveau Personnel</span>
                        </RouterLink>
                    </Button>
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead>
                                Nom complet
                            </TableHead>
                            <TableHead>
                                Classe
                            </TableHead>
                            <TableHead>
                                Présence
                            </TableHead>
                            <TableHead>
                                Commentaire
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="student in studentsPresences" :key="student.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>
                                {{ student.nom }} {{ student.postnom }} {{ student.prenom }}
                            </TableCell>
                            <TableCell>
                                {{ student.classe }} - {{ student.filiere }}
                            </TableCell>
                            <TableCell>
                                <Switch :id="`presence-${student.id}`" :checked="student.present" />
                            </TableCell>
                            <TableCell class="text-left max-w-xs">
                                <Input :value="student.commentaire" placeholder="Ajouter un commentaire..."
                                    class="h-8 text-xs border-gray-200" />
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </SaisieRhLayout>
</template>
