<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';
import SaisieRhLayout from '@/components/templates/rh/SaisieRhLayout.vue';



const personnelPresences = [
    {
        id: 1,
        nom: "Mwamba",
        postnom: "Kabasele",
        prenom: "Jean",
        matricule: "PERS001",
        sexe: "M",
        poste: "Professeur",
        departement: "Lettres",
        statut: "Titulaire",
        present: true,
        heureArrivee: "07:30",
        heureDepart: "",
        commentaire: "Présent à l'heure"
    },
    {
        id: 2,
        nom: "Kason<PERSON>",
        postnom: "Mutom<PERSON>",
        prenom: "<PERSON>",
        matricule: "PERS002",
        sexe: "F",
        poste: "Secrétaire",
        departement: "Administration",
        statut: "Contractuel",
        present: false,
        heureArrivee: "",
        heureDepart: "",
        commentaire: "Congé maladie"
    },
    {
        id: 3,
        nom: "Ilunga",
        postnom: "Kabeya",
        prenom: "Patrick",
        matricule: "PERS003",
        sexe: "M",
        poste: "Directeur",
        departement: "Direction",
        statut: "Titulaire",
        present: true,
        heureArrivee: "07:00",
        heureDepart: "",
        commentaire: "Présent"
    },
    {
        id: 4,
        nom: "Tshibanda",
        postnom: "Mbuyi",
        prenom: "Aline",
        matricule: "PERS004",
        sexe: "F",
        poste: "Professeur",
        departement: "Sciences",
        statut: "Titulaire",
        present: true,
        heureArrivee: "08:00",
        heureDepart: "",
        commentaire: "Présente"
    },
    {
        id: 5,
        nom: "Kabongo",
        postnom: "Mukendi",
        prenom: "David",
        matricule: "PERS005",
        sexe: "M",
        poste: "Surveillant",
        departement: "Discipline",
        statut: "Contractuel",
        present: true,
        heureArrivee: "08:15",
        heureDepart: "",
        commentaire: "Retard de 15 minutes"
    }
]
</script>

<template>
    <SaisieRhLayout activeBread="Presence" active-tag-name="presence" group="saisie">
        <BoxPanelWrapper>
            <div class="flex sm:items-center gap-3 flex-col sm:flex-row sm:justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher un personnel..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center sm:justify-end gap-2.5 flex-1">
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead>
                                Nom complet
                            </TableHead>
                            <TableHead>
                                Poste
                            </TableHead>
                            <TableHead>
                                Département
                            </TableHead>
                            <TableHead>
                                Heure d'arrivée
                            </TableHead>
                            <TableHead>
                                Présence
                            </TableHead>
                            <TableHead>
                                Commentaire
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="personnel in personnelPresences" :key="personnel.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>
                                {{ personnel.nom }} {{ personnel.postnom }} {{ personnel.prenom }}
                            </TableCell>
                            <TableCell>
                                {{ personnel.poste }}
                            </TableCell>
                            <TableCell>
                                {{ personnel.departement }}
                            </TableCell>
                            <TableCell>
                                <Input :value="personnel.heureArrivee" placeholder="--:--"
                                    class="h-8 text-xs border-gray-200 w-24" type="time" />
                            </TableCell>
                            <TableCell>
                                <Switch :id="`presence-${personnel.id}`" :checked="personnel.present" />
                            </TableCell>
                            <TableCell class="text-left max-w-xs">
                                <Input :value="personnel.commentaire" placeholder="Ajouter un commentaire..."
                                    class="h-8 text-xs border-gray-200" />
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </SaisieRhLayout>
</template>
