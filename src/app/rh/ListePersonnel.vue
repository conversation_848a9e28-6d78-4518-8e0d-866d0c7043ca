<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';
import SaisieRhLayout from '@/components/templates/rh/SaisieRhLayout.vue';



const personnel = [
    {
        id: 1,
        matricule: "EMP001",
        name: "<PERSON><PERSON><PERSON>",
        firstname: "<PERSON>",
        username: "jean.mwamba",
        phone_number: "+243 812 345 678",
        email: "<EMAIL>",
        identity_card: "1234567890123",
        gender: "<PERSON><PERSON><PERSON><PERSON>",
        civil_status: "<PERSON><PERSON>",
        birth_date: "1985-03-15",
        birth_place: "Kinshasa",
        address: "Avenue Kasa-Vubu, Kinshasa"
    },
    {
        id: 2,
        matricule: "EMP002",
        name: "<PERSON><PERSON><PERSON>",
        firstname: "<PERSON>",
        username: "sarah.kasongo",
        phone_number: "+243 823 456 789",
        email: "<EMAIL>",
        identity_card: "2345678901234",
        gender: "Féminin",
        civil_status: "Célibataire",
        birth_date: "1990-07-22",
        birth_place: "Lubumbashi",
        address: "Boulevard Mobutu, Lubumbashi"
    },
    {
        id: 3,
        matricule: "EMP003",
        name: "Ilunga",
        firstname: "Patrick",
        username: "patrick.ilunga",
        phone_number: "+243 834 567 890",
        email: "<EMAIL>",
        identity_card: "3456789012345",
        gender: "Masculin",
        civil_status: "Divorcé",
        birth_date: "1982-11-08",
        birth_place: "Mbuji-Mayi",
        address: "Rue de la Paix, Mbuji-Mayi"
    },
    {
        id: 4,
        matricule: "EMP004",
        name: "Tshibanda",
        firstname: "Aline",
        username: "aline.tshibanda",
        phone_number: "+243 845 678 901",
        email: "<EMAIL>",
        identity_card: "*************",
        gender: "Féminin",
        civil_status: "Mariée",
        birth_date: "1988-05-12",
        birth_place: "Kananga",
        address: "Avenue Lumumba, Kananga"
    },
]
</script>

<template>
    <SaisieRhLayout activeBread="Personnel" active-tag-name="personnel" group="saisie">
        <BoxPanelWrapper>
            <div class="flex sm:items-center gap-3 flex-col sm:flex-row sm:justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher un personnel..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center sm:justify-end gap-2.5 flex-1">
                    <Button size="md" class="rounded-md max-sm:flex-1 sm:w-max" as-child>
                        <RouterLink to="/rh/operations/nouveau-personnel">
                            <span class="flex iconify hugeicons--plus-sign"></span>
                            <span class="hidden sm:flex">Nouveau Personnel</span>
                        </RouterLink>
                    </Button>
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead>Matricule</TableHead>
                            <TableHead>Nom</TableHead>
                            <TableHead>Prénom</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Téléphone</TableHead>
                            <TableHead>Genre</TableHead>
                            <TableHead>État civil</TableHead>
                            <TableHead>Date de naissance</TableHead>
                            <TableHead>
                                Opérations
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="item in personnel" :key="item.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>{{ item.matricule }}</TableCell>
                            <TableCell>{{ item.name }}</TableCell>
                            <TableCell>{{ item.firstname }}</TableCell>
                            <TableCell>{{ item.email }}</TableCell>
                            <TableCell>{{ item.phone_number }}</TableCell>
                            <TableCell>{{ item.gender }}</TableCell>
                            <TableCell>{{ item.civil_status }}</TableCell>
                            <TableCell>{{ item.birth_date }}</TableCell>
                            <TableCell>
                                <div class="flex items-center gap-2 w-max">
                                    <Button variant="outline" size="icon" class="size-8">
                                        <span class="iconify hugeicons--edit-02"></span>
                                    </Button>
                                    <Button variant="destructive" size="icon" class="size-8">
                                        <span class="iconify hugeicons--delete-02"></span>
                                    </Button>
                                </div>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </SaisieRhLayout>
</template>
