<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';
import SaisieRhLayout from '@/components/templates/rh/SaisieRhLayout.vue';
import NouveauConge from '@/components/modals/NouveauConge.vue';



const congesPersonnel = [
    {
        id: 1,
        matricule: "PERS001",
        nom: "Mwamba",
        prenom: "<PERSON>",
        poste: "Professeur",
        departement: "Lettres",
        typeConge: "Congé annuel",
        dateDebut: "2024-08-15",
        dateFin: "2024-08-29",
        duree: 14,
        motif: "Vacances familiales",
        statut: "Approuvé",
        dateDemandeConge: "2024-07-20",
        approuvePar: "Directeur RH"
    },
    {
        id: 2,
        matricule: "PERS002",
        nom: "Kasongo",
        prenom: "Sarah",
        poste: "Secrétaire",
        departement: "Administration",
        typeConge: "Congé maladie",
        dateDebut: "2024-07-28",
        dateFin: "2024-08-02",
        duree: 5,
        motif: "Intervention chirurgicale",
        statut: "En cours",
        dateDemandeConge: "2024-07-25",
        approuvePar: ""
    },
    {
        id: 3,
        matricule: "PERS003",
        nom: "Ilunga",
        prenom: "Patrick",
        poste: "Directeur",
        departement: "Direction",
        typeConge: "Congé de maternité",
        dateDebut: "2024-09-01",
        dateFin: "2024-11-30",
        duree: 90,
        motif: "Naissance",
        statut: "Planifié",
        dateDemandeConge: "2024-06-15",
        approuvePar: "Conseil d'administration"
    },
    {
        id: 4,
        matricule: "PERS004",
        nom: "Tshibanda",
        prenom: "Aline",
        poste: "Professeur",
        departement: "Sciences",
        typeConge: "Congé sans solde",
        dateDebut: "2024-10-01",
        dateFin: "2024-12-31",
        duree: 92,
        motif: "Formation continue",
        statut: "Rejeté",
        dateDemandeConge: "2024-07-10",
        approuvePar: ""
    },
    {
        id: 5,
        matricule: "PERS005",
        nom: "Kabongo",
        prenom: "David",
        poste: "Surveillant",
        departement: "Discipline",
        typeConge: "Congé exceptionnel",
        dateDebut: "2024-08-05",
        dateFin: "2024-08-07",
        duree: 3,
        motif: "Décès familial",
        statut: "Approuvé",
        dateDemandeConge: "2024-08-03",
        approuvePar: "Directeur"
    }
]
</script>

<template>
    <SaisieRhLayout activeBread="Conge" active-tag-name="conge" group="saisie">
        <BoxPanelWrapper>
            <div class="flex sm:items-center gap-3 flex-col sm:flex-row sm:justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher un personnel..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center sm:justify-end gap-2.5 flex-1">
                    <NouveauConge/>
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead>Personnel</TableHead>
                            <TableHead>Poste</TableHead>
                            <TableHead>Type de congé</TableHead>
                            <TableHead>Date début</TableHead>
                            <TableHead>Date fin</TableHead>
                            <TableHead>Durée</TableHead>
                            <TableHead>Statut</TableHead>
                            <TableHead>Motif</TableHead>
                            <TableHead>
                                Opérations
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="conge in congesPersonnel" :key="conge.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>
                                <div class="flex flex-col">
                                    <span class="font-medium">{{ conge.nom }} {{ conge.prenom }}</span>
                                    <span class="text-xs text-muted-foreground">{{ conge.matricule }}</span>
                                </div>
                            </TableCell>
                            <TableCell>
                                <div class="flex flex-col">
                                    <span>{{ conge.poste }}</span>
                                    <span class="text-xs text-muted-foreground">{{ conge.departement }}</span>
                                </div>
                            </TableCell>
                            <TableCell>{{ conge.typeConge }}</TableCell>
                            <TableCell>{{ conge.dateDebut }}</TableCell>
                            <TableCell>{{ conge.dateFin }}</TableCell>
                            <TableCell>{{ conge.duree }} jours</TableCell>
                            <TableCell>
                                <Badge :variant="conge.statut === 'Approuvé' ? 'default' :
                                    conge.statut === 'Rejeté' ? 'destructive' :
                                        conge.statut === 'En cours' ? 'secondary' : 'outline'">
                                    {{ conge.statut }}
                                </Badge>
                            </TableCell>
                            <TableCell class="max-w-xs">
                                <span class="text-sm">{{ conge.motif }}</span>
                            </TableCell>
                            <TableCell>
                                <div class="flex items-center gap-2 w-max">
                                    <Button variant="outline" size="icon" class="size-8">
                                        <span class="iconify hugeicons--view"></span>
                                    </Button>
                                    <Button variant="outline" size="icon" class="size-8">
                                        <span class="iconify hugeicons--edit-02"></span>
                                    </Button>
                                    <Button variant="destructive" size="icon" class="size-8">
                                        <span class="iconify hugeicons--delete-02"></span>
                                    </Button>
                                </div>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </SaisieRhLayout>
</template>
