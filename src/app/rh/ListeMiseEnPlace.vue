<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import BoxPanelWrapper from '@/components/atoms/BoxPanelWrapper.vue';
import SaisieRhLayout from '@/components/templates/rh/SaisieRhLayout.vue';



const miseEnPlacePersonnel = [
    {
        id: 1,
        matricule: "PERS001",
        nom: "Mwamba",
        prenom: "Jean",
        ancienPoste: "Assistant",
        nouveauPoste: "Professeur",
        ancienDepartement: "Formation",
        nouveauDepartement: "Lettres",
        typeAffectation: "Promotion",
        dateEffet: "2024-09-01",
        salairePrecedent: 800000,
        nouveauSalaire: 1200000,
        motif: "Promotion suite à l'obtention du diplôme de master",
        statut: "Approuvé",
        dateDecision: "2024-07-15",
        approuvePar: "Directeur RH"
    },
    {
        id: 2,
        matricule: "PERS002",
        nom: "Kasongo",
        prenom: "Sarah",
        ancienPoste: "Secrétaire",
        nouveauPoste: "Secrétaire principale",
        ancienDepartement: "Administration",
        nouveauDepartement: "Direction",
        typeAffectation: "Mutation",
        dateEffet: "2024-08-15",
        salairePrecedent: 600000,
        nouveauSalaire: 750000,
        motif: "Réorganisation des services administratifs",
        statut: "En cours",
        dateDecision: "2024-07-30",
        approuvePar: ""
    },
    {
        id: 3,
        matricule: "PERS003",
        nom: "Ilunga",
        prenom: "Patrick",
        ancienPoste: "Professeur",
        nouveauPoste: "Directeur adjoint",
        ancienDepartement: "Mathématiques",
        nouveauDepartement: "Direction",
        typeAffectation: "Nomination",
        dateEffet: "2024-10-01",
        salairePrecedent: 1000000,
        nouveauSalaire: 1500000,
        motif: "Nomination au poste de direction",
        statut: "Planifié",
        dateDecision: "2024-06-20",
        approuvePar: "Conseil d'administration"
    },
    {
        id: 4,
        matricule: "PERS004",
        nom: "Tshibanda",
        prenom: "Aline",
        ancienPoste: "Professeur",
        nouveauPoste: "Professeur",
        ancienDepartement: "Sciences",
        nouveauDepartement: "Lettres",
        typeAffectation: "Transfert",
        dateEffet: "2024-09-15",
        salairePrecedent: 950000,
        nouveauSalaire: 950000,
        motif: "Besoin de renforcement du département Lettres",
        statut: "Rejeté",
        dateDecision: "2024-07-25",
        approuvePar: ""
    },
    {
        id: 5,
        matricule: "PERS005",
        nom: "Kabongo",
        prenom: "David",
        ancienPoste: "Surveillant",
        nouveauPoste: "Chef surveillant",
        ancienDepartement: "Discipline",
        nouveauDepartement: "Discipline",
        typeAffectation: "Promotion",
        dateEffet: "2024-08-01",
        salairePrecedent: 500000,
        nouveauSalaire: 650000,
        motif: "Reconnaissance de l'expérience et des compétences",
        statut: "Approuvé",
        dateDecision: "2024-07-10",
        approuvePar: "Directeur"
    }
]
</script>

<template>
    <SaisieRhLayout activeBread="Mise en place" active-tag-name="mise-en-place" group="saisie">
        <BoxPanelWrapper>
            <div class="flex sm:items-center gap-3 flex-col sm:flex-row sm:justify-between">
                <div class="relative flex-1">
                    <Input type="text" id="search" name="search" placeholder="Rechercher une affectation..."
                        class="w-full max-w-sm ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-md" />
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                        <span class="flex iconify hugeicons--search-01 text-sm"></span>
                    </div>
                </div>
                <div class="flex flex-wrap items-center sm:justify-end gap-2.5 flex-1">
                    <Button size="md" class="rounded-md max-sm:flex-1 sm:w-max">
                        <span class="flex iconify hugeicons--plus-sign"></span>
                        <span class="hidden sm:flex">Nouvelle affectation</span>
                    </Button>
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <Button variant="ghost" size="md" class="bg-white border border-border rounded-md">
                                Exporter
                                <span class="iconify hugeicons--arrow-down-01 " />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </DropdownMenuItem>
                            <DropdownMenuItem class="flex items-center">
                                <span class="flex mr-1.5 iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            <div class="mt-4 rounded-md overflow-hidden">
                <Table class="rounded-md bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead>Personnel</TableHead>
                            <TableHead>Poste</TableHead>
                            <TableHead>Type de congé</TableHead>
                            <TableHead>Date début</TableHead>
                            <TableHead>Date fin</TableHead>
                            <TableHead>Durée</TableHead>
                            <TableHead>Statut</TableHead>
                            <TableHead>Motif</TableHead>
                            <TableHead>
                                Opérations
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="conge in congesPersonnel" :key="conge.id">
                            <TableCell class="w-[40px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableCell>
                            <TableCell>
                                <div class="flex flex-col">
                                    <span class="font-medium">{{ conge.nom }} {{ conge.prenom }}</span>
                                    <span class="text-xs text-muted-foreground">{{ conge.matricule }}</span>
                                </div>
                            </TableCell>
                            <TableCell>
                                <div class="flex flex-col">
                                    <span>{{ conge.poste }}</span>
                                    <span class="text-xs text-muted-foreground">{{ conge.departement }}</span>
                                </div>
                            </TableCell>
                            <TableCell>{{ conge.typeConge }}</TableCell>
                            <TableCell>{{ conge.dateDebut }}</TableCell>
                            <TableCell>{{ conge.dateFin }}</TableCell>
                            <TableCell>{{ conge.duree }} jours</TableCell>
                            <TableCell>
                                <Badge :variant="conge.statut === 'Approuvé' ? 'default' :
                                    conge.statut === 'Rejeté' ? 'destructive' :
                                        conge.statut === 'En cours' ? 'secondary' : 'outline'">
                                    {{ conge.statut }}
                                </Badge>
                            </TableCell>
                            <TableCell class="max-w-xs">
                                <span class="text-sm">{{ conge.motif }}</span>
                            </TableCell>
                            <TableCell>
                                <div class="flex items-center gap-2 w-max">
                                    <Button variant="outline" size="icon" class="size-8">
                                        <span class="iconify hugeicons--view"></span>
                                    </Button>
                                    <Button variant="outline" size="icon" class="size-8">
                                        <span class="iconify hugeicons--edit-02"></span>
                                    </Button>
                                    <Button variant="destructive" size="icon" class="size-8">
                                        <span class="iconify hugeicons--delete-02"></span>
                                    </Button>
                                </div>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
        </BoxPanelWrapper>
    </SaisieRhLayout>
</template>
