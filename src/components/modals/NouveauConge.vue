<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
</script>

<template>
    <Dialog>
        <DialogTrigger as-child>
            <Button size="md" class="rounded-md">
                <span class="iconify hugeicons--plus-sign"></span>
                <span class="hidden sm:flex"> Nouveau congé</span>
            </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-[700px]">
            <DialogHeader>
                <DialogTitle>Demande de congé</DialogTitle>
                <DialogDescription>
                    Créer une nouvelle demande de congé pour le personnel
                </DialogDescription>
            </DialogHeader>
            <div class="grid gap-4 py-4">
                <!-- Block 1: Informations du personnel -->
                <div class="grid sm:grid-cols-2 gap-x-6 gap-y-3">
                    <div class="flex flex-col space-y-1.5">
                        <Label for="personnel" class="text-sm font-medium">
                            Personnel
                        </Label>
                        <Select>
                            <SelectTrigger id="personnel" class="!h-10 bg-white w-full">
                                <SelectValue placeholder="Sélectionnez le personnel" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    <SelectItem value="pers001">Jean Mwamba Kabasele (PERS001)</SelectItem>
                                    <SelectItem value="pers002">Sarah Kasongo Mutombo (PERS002)</SelectItem>
                                    <SelectItem value="pers003">Patrick Ilunga Kabeya (PERS003)</SelectItem>
                                    <SelectItem value="pers004">Aline Tshibanda Mbuyi (PERS004)</SelectItem>
                                    <SelectItem value="pers005">David Kabongo Mukendi (PERS005)</SelectItem>
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                    </div>
                    <div class="flex flex-col space-y-1.5">
                        <Label for="type_conge" class="text-sm font-medium">
                            Type de congé
                        </Label>
                        <Select>
                            <SelectTrigger id="type_conge" class="!h-10 bg-white w-full">
                                <SelectValue placeholder="Sélectionnez le type de congé" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    <SelectItem value="annuel">Congé annuel</SelectItem>
                                    <SelectItem value="maladie">Congé maladie</SelectItem>
                                    <SelectItem value="maternite">Congé de maternité</SelectItem>
                                    <SelectItem value="paternite">Congé de paternité</SelectItem>
                                    <SelectItem value="sans_solde">Congé sans solde</SelectItem>
                                    <SelectItem value="exceptionnel">Congé exceptionnel</SelectItem>
                                    <SelectItem value="formation">Congé de formation</SelectItem>
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                    </div>
                    <div class="flex flex-col space-y-1.5">
                        <Label for="date_debut" class="text-sm font-medium">
                            Date de début
                        </Label>
                        <Input type="date" id="date_debut" name="date_debut"
                            class="w-full h-10 border border-gray-200/40 bg-white transition-all" />
                    </div>
                    <div class="flex flex-col space-y-1.5">
                        <Label for="date_fin" class="text-sm font-medium">
                            Date de fin
                        </Label>
                        <Input type="date" id="date_fin" name="date_fin"
                            class="w-full h-10 border border-gray-200/40 bg-white transition-all" />
                    </div>
                    <div class="flex flex-col space-y-1.5">
                        <Label for="duree" class="text-sm font-medium">
                            Durée (jours)
                        </Label>
                        <Input type="number" id="duree" name="duree" placeholder="Nombre de jours" readonly
                            class="w-full h-10 border border-gray-200/40 bg-gray-50 transition-all" />
                    </div>
                    <div class="flex flex-col space-y-1.5">
                        <Label for="date_demande" class="text-sm font-medium">
                            Date de demande
                        </Label>
                        <Input type="date" id="date_demande" name="date_demande"
                            class="w-full h-10 border border-gray-200/40 bg-white transition-all" />
                    </div>
                </div>
                <!-- Block 2: Détails et justification -->
                <div class="grid gap-3">
                    <div class="flex flex-col space-y-1.5">
                        <Label for="motif" class="text-sm font-medium">
                            Motif du congé
                        </Label>
                        <Textarea id="motif" name="motif" placeholder="Décrivez le motif de votre demande de congé..."
                            class="w-full min-h-[80px] border border-gray-200/40 bg-white transition-all resize-none" />
                    </div>
                    <div class="grid sm:grid-cols-2 gap-x-6 gap-y-3">
                        <div class="flex flex-col space-y-1.5">
                            <Label for="remplacant" class="text-sm font-medium">
                                Remplaçant (optionnel)
                            </Label>
                            <Select>
                                <SelectTrigger id="remplacant" class="!h-10 bg-white w-full">
                                    <SelectValue placeholder="Sélectionnez un remplaçant" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="pers001">Jean Mwamba Kabasele</SelectItem>
                                        <SelectItem value="pers002">Sarah Kasongo Mutombo</SelectItem>
                                        <SelectItem value="pers003">Patrick Ilunga Kabeya</SelectItem>
                                        <SelectItem value="pers004">Aline Tshibanda Mbuyi</SelectItem>
                                        <SelectItem value="pers005">David Kabongo Mukendi</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="flex flex-col space-y-1.5">
                            <Label for="priorite" class="text-sm font-medium">
                                Priorité
                            </Label>
                            <Select>
                                <SelectTrigger id="priorite" class="!h-10 bg-white w-full">
                                    <SelectValue placeholder="Sélectionnez la priorité" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="normale">Normale</SelectItem>
                                        <SelectItem value="urgente">Urgente</SelectItem>
                                        <SelectItem value="tres_urgente">Très urgente</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-1.5">
                        <Label for="commentaires" class="text-sm font-medium">
                            Commentaires additionnels (optionnel)
                        </Label>
                        <Textarea id="commentaires" name="commentaires"
                            placeholder="Ajoutez des informations supplémentaires si nécessaire..."
                            class="w-full min-h-[60px] border border-gray-200/40 bg-white transition-all resize-none" />
                    </div>
                </div>
            </div>
            <DialogFooter class="flex justify-end gap-2 items-center">
                <Button size="sm" class="h-9" type="submit">
                    Soumettre la demande
                </Button>
                <Button size="sm" class="h-9" variant="outline" type="button">
                    Annuler
                </Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>